// Application constants
export const APP_CONFIG = {
  NAME: 'Toolkit Engine',
  VERSION: '1.0.0',
  COMPANY: 'VIS Solutions',
  DATABASE_NAME: 'projects.db',
  DATABASE_FOLDER: 'Toolkit Engine'
};

// Project categories
export const PROJECT_CATEGORIES = {
  LIGHTING: 'lighting',
  AIRCON: 'aircon',
  UNIT: 'unit',
  CURTAIN: 'curtain',
  SCENE: 'scene'
};

// Project category labels and icons
export const CATEGORY_CONFIG = {
  [PROJECT_CATEGORIES.LIGHTING]: {
    label: 'Lighting',
    icon: 'Lightbulb',
    description: 'Lighting control systems'
  },
  [PROJECT_CATEGORIES.AIRCON]: {
    label: 'Aircon',
    icon: 'Wind',
    description: 'Air conditioning systems'
  },
  [PROJECT_CATEGORIES.UNIT]: {
    label: 'Unit',
    icon: 'Cpu',
    description: 'Control units and devices'
  },
  [PROJECT_CATEGORIES.CURTAIN]: {
    label: 'Curtain',
    icon: 'Blinds',
    description: 'Curtain and blind controls'
  },
  [PROJECT_CATEGORIES.SCENE]: {
    label: 'Scene',
    icon: 'Palette',
    description: 'Scene management'
  }
};

// Unit types for dropdown
export const UNIT_TYPES = [
  'Controller',
  'Sensor',
  'Actuator',
  'Gateway',
  'Switch',
  'Dimmer',
  'Relay',
  'Other'
];

// Unit modes
export const UNIT_MODES = [
  'Slave',
  'Master',
  'Stand Alone'
];

// Validation limits
export const VALIDATION_LIMITS = {
  PROJECT_NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100
  },
  ITEM_NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100
  },
  DESCRIPTION: {
    MAX_LENGTH: 500
  },
  ADDRESS: {
    MIN_VALUE: 1,
    MAX_VALUE: 65535
  },
  SERIAL_NUMBER: {
    MAX_LENGTH: 50
  },
  FIRMWARE_VERSION: {
    MAX_LENGTH: 20
  },
  ID_CAN: {
    MIN_VALUE: 0,
    MAX_VALUE: 255
  }
};

// Table pagination
export const TABLE_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 20, 50],
  MAX_ROWS_PER_PAGE: 100
};

// UI constants
export const UI_CONFIG = {
  SIDEBAR_WIDTH: '280px',
  SIDEBAR_WIDTH_MOBILE: '100%',
  MOBILE_BREAKPOINT: 768,
  ANIMATION_DURATION: 200,
  TOAST_DURATION: 3000
};

// Error messages
export const ERROR_MESSAGES = {
  GENERIC: 'An unexpected error occurred',
  NETWORK: 'Network connection error',
  DATABASE: 'Database operation failed',
  VALIDATION: 'Please check your input and try again',
  NOT_FOUND: 'The requested item was not found',
  PERMISSION_DENIED: 'You do not have permission to perform this action',
  PROJECT: {
    CREATE_FAILED: 'Failed to create project',
    UPDATE_FAILED: 'Failed to update project',
    DELETE_FAILED: 'Failed to delete project',
    DUPLICATE_FAILED: 'Failed to duplicate project',
    LOAD_FAILED: 'Failed to load projects',
    NOT_FOUND: 'Project not found'
  },
  ITEM: {
    CREATE_FAILED: 'Failed to create item',
    UPDATE_FAILED: 'Failed to update item',
    DELETE_FAILED: 'Failed to delete item',
    DUPLICATE_FAILED: 'Failed to duplicate item',
    LOAD_FAILED: 'Failed to load items'
  }
};

// Success messages
export const SUCCESS_MESSAGES = {
  PROJECT: {
    CREATED: 'Project created successfully',
    UPDATED: 'Project updated successfully',
    DELETED: 'Project deleted successfully',
    DUPLICATED: 'Project duplicated successfully'
  },
  ITEM: {
    CREATED: 'Item created successfully',
    UPDATED: 'Item updated successfully',
    DELETED: 'Item deleted successfully',
    DUPLICATED: 'Item duplicated successfully'
  }
};

// Dialog modes
export const DIALOG_MODES = {
  CREATE: 'create',
  EDIT: 'edit',
  VIEW: 'view'
};

// Loading states
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
};

// IPC channels
export const IPC_CHANNELS = {
  PROJECTS: {
    GET_ALL: 'projects:getAll',
    GET_BY_ID: 'projects:getById',
    CREATE: 'projects:create',
    UPDATE: 'projects:update',
    DELETE: 'projects:delete',
    DUPLICATE: 'projects:duplicate',
    GET_ALL_ITEMS: 'projects:getAllItems'
  },
  LIGHTING: {
    GET_ALL: 'lighting:getAll',
    CREATE: 'lighting:create',
    UPDATE: 'lighting:update',
    DELETE: 'lighting:delete',
    DUPLICATE: 'lighting:duplicate'
  },
  AIRCON: {
    GET_ALL: 'aircon:getAll',
    CREATE: 'aircon:create',
    UPDATE: 'aircon:update',
    DELETE: 'aircon:delete',
    DUPLICATE: 'aircon:duplicate'
  },
  UNIT: {
    GET_ALL: 'unit:getAll',
    CREATE: 'unit:create',
    UPDATE: 'unit:update',
    DELETE: 'unit:delete',
    DUPLICATE: 'unit:duplicate'
  },
  CURTAIN: {
    GET_ALL: 'curtain:getAll',
    CREATE: 'curtain:create',
    UPDATE: 'curtain:update',
    DELETE: 'curtain:delete',
    DUPLICATE: 'curtain:duplicate'
  },
  SCENE: {
    GET_ALL: 'scene:getAll',
    CREATE: 'scene:create',
    UPDATE: 'scene:update',
    DELETE: 'scene:delete',
    DUPLICATE: 'scene:duplicate'
  }
};

// Regular expressions
export const REGEX_PATTERNS = {
  IP_ADDRESS: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  POSITIVE_INTEGER: /^\d+$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  VERSION: /^\d+\.\d+\.\d+$/
};

// Default values
export const DEFAULT_VALUES = {
  PROJECT: {
    name: '',
    description: ''
  },
  PROJECT_ITEM: {
    name: '',
    address: '',
    description: ''
  },
  UNIT_ITEM: {
    name: '',
    type: '',
    serial_no: '',
    ip_address: '',
    id_can: '',
    mode: '',
    firmware_version: '',
    description: ''
  }
};
