import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
    return twMerge(clsx(inputs));
}

// Cache for regex patterns to avoid recreating them
const REGEX_CACHE = {
    IP_ADDRESS: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    DANGEROUS_CHARS: /[<>]/g
};

// Validation utilities
export const validation = {
    // Project validation
    validateProject: (data) => {
        const errors = {};

        if (!data.name || data.name.trim().length === 0) {
            errors.name = "Project name is required";
        } else if (data.name.trim().length < 2) {
            errors.name = "Project name must be at least 2 characters";
        } else if (data.name.trim().length > 100) {
            errors.name = "Project name must be less than 100 characters";
        }

        if (data.description && data.description.length > 500) {
            errors.description = "Description must be less than 500 characters";
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    },

    // Project item validation
    validateProjectItem: (data) => {
        const errors = {};

        if (!data.name || data.name.trim().length === 0) {
            errors.name = "Item name is required";
        } else if (data.name.trim().length < 2) {
            errors.name = "Item name must be at least 2 characters";
        } else if (data.name.trim().length > 100) {
            errors.name = "Item name must be less than 100 characters";
        }

        if (data.address) {
            const addressNum = parseInt(data.address);
            if (isNaN(addressNum) || addressNum <= 0) {
                errors.address = "Address must be a positive integer";
            } else if (addressNum > 65535) {
                errors.address = "Address must be less than 65536";
            }
        }

        if (data.description && data.description.length > 500) {
            errors.description = "Description must be less than 500 characters";
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    },

    // Unit item validation (has additional fields)
    validateUnitItem: (data) => {
        const baseValidation = validation.validateProjectItem(data);
        const errors = { ...baseValidation.errors };

        // Type validation
        if (!data.type || data.type.trim().length === 0) {
            errors.type = "Type is required";
        }

        // Serial number validation
        if (data.serial_no && data.serial_no.length > 50) {
            errors.serial_no = "Serial number must be less than 50 characters";
        }

        // IP Address validation
        if (data.ip_address) {
            if (!REGEX_CACHE.IP_ADDRESS.test(data.ip_address)) {
                errors.ip_address = "Invalid IP address format";
            }
        }

        // ID CAN validation
        if (data.id_can) {
            const idCanNum = parseInt(data.id_can);
            if (isNaN(idCanNum) || idCanNum < 0 || idCanNum > 255) {
                errors.id_can = "ID CAN must be between 0 and 255";
            }
        }

        // Mode validation
        if (data.mode && !['Slave', 'Master', 'Stand Alone'].includes(data.mode)) {
            errors.mode = "Mode must be Slave, Master, or Stand Alone";
        }

        // Firmware version validation
        if (data.firmware_version && data.firmware_version.length > 20) {
            errors.firmware_version = "Firmware version must be less than 20 characters";
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    },

    // Sanitize input data
    sanitizeInput: (data) => {
        const sanitized = {};

        Object.keys(data).forEach(key => {
            if (typeof data[key] === 'string') {
                // Trim whitespace and remove potentially dangerous characters
                sanitized[key] = data[key].trim().replace(REGEX_CACHE.DANGEROUS_CHARS, '');
            } else {
                sanitized[key] = data[key];
            }
        });

        return sanitized;
    }
};