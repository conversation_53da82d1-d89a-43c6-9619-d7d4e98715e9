import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useProjects } from "@/contexts/project-context";
import { validation } from "@/lib/utils";
import {
  DEFAULT_VALUES,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
} from "@/lib/constants";
import { toast } from "sonner";

export function ProjectDialog({
  open,
  onOpenChange,
  project = null,
  mode = "create",
}) {
  const { createProject, updateProject } = useProjects();
  const [formData, setFormData] = useState(DEFAULT_VALUES.PROJECT);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Reset form when dialog opens/closes or project changes
  useEffect(() => {
    if (open) {
      setErrors({}); // Clear errors when dialog opens
      if (mode === "edit" && project) {
        setFormData({
          name: project.name || "",
          description: project.description || "",
        });
      } else {
        setFormData(DEFAULT_VALUES.PROJECT);
      }
    }
  }, [open, project, mode]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Sanitize input data
    const sanitizedData = validation.sanitizeInput(formData);

    // Validate form data
    const validationResult = validation.validateProject(sanitizedData);

    if (!validationResult.isValid) {
      setErrors(validationResult.errors);
      return;
    }

    setLoading(true);
    try {
      if (mode === "edit" && project) {
        await updateProject(project.id, sanitizedData);
        toast.success(SUCCESS_MESSAGES.PROJECT.UPDATED);
      } else {
        await createProject(sanitizedData);
        toast.success(SUCCESS_MESSAGES.PROJECT.CREATED);
      }
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to save project:", error);
      const errorMessage =
        mode === "edit"
          ? ERROR_MESSAGES.PROJECT.UPDATE_FAILED
          : ERROR_MESSAGES.PROJECT.CREATE_FAILED;
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: null,
      }));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === "edit" ? "Edit Project" : "Create New Project"}
          </DialogTitle>
          <DialogDescription>
            {mode === "edit"
              ? "Update your project details below."
              : "Add a new project to your workspace."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter project name"
              className={
                errors.name ? "border-red-500 focus:border-red-500" : ""
              }
              required
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter project description (optional)"
              className={
                errors.description ? "border-red-500 focus:border-red-500" : ""
              }
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                loading || Object.keys(errors).some((key) => errors[key])
              }
            >
              {loading ? "Saving..." : mode === "edit" ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
