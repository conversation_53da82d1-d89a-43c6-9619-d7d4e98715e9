import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useProjectDetail } from "@/contexts/project-detail-context";
import { validation } from "@/lib/utils";
import {
  CATEGORY_CONFIG,
  DEFAULT_VALUES,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
} from "@/lib/constants";
import { toast } from "sonner";

export function ProjectItemDialog({
  open,
  onOpenChange,
  category,
  item = null,
  mode = "create",
}) {
  const { createItem, updateItem } = useProjectDetail();
  const [formData, setFormData] = useState(DEFAULT_VALUES.PROJECT_ITEM);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Reset form when dialog opens/closes or item changes
  useEffect(() => {
    if (open) {
      setErrors({}); // Clear errors when dialog opens
      if (mode === "edit" && item) {
        setFormData({
          name: item.name || "",
          address: item.address || "",
          description: item.description || "",
        });
      } else {
        setFormData(DEFAULT_VALUES.PROJECT_ITEM);
      }
    }
  }, [open, item, mode]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Sanitize input data
    const sanitizedData = validation.sanitizeInput(formData);

    // Validate form data
    const validationResult = validation.validateProjectItem(sanitizedData);

    if (!validationResult.isValid) {
      setErrors(validationResult.errors);
      return;
    }

    setLoading(true);
    try {
      if (mode === "edit" && item) {
        await updateItem(category, item.id, sanitizedData);
        toast.success(SUCCESS_MESSAGES.ITEM.UPDATED);
      } else {
        await createItem(category, sanitizedData);
        toast.success(SUCCESS_MESSAGES.ITEM.CREATED);
      }
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to save item:", error);
      const errorMessage =
        mode === "edit"
          ? ERROR_MESSAGES.ITEM.UPDATE_FAILED
          : ERROR_MESSAGES.ITEM.CREATE_FAILED;
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: null,
      }));
    }
  };

  const categoryLabel = CATEGORY_CONFIG[category]?.label || category;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === "edit"
              ? `Edit ${categoryLabel} Item`
              : `Create New ${categoryLabel} Item`}
          </DialogTitle>
          <DialogDescription>
            {mode === "edit"
              ? `Update the ${categoryLabel.toLowerCase()} item details below.`
              : `Add a new ${categoryLabel.toLowerCase()} item to your project.`}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="name" className="text-right pt-2">
                Name *
              </Label>
              <div className="col-span-3">
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className={
                    errors.name ? "border-red-500 focus:border-red-500" : ""
                  }
                  placeholder="Enter item name"
                  required
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="address" className="text-right pt-2">
                Address
              </Label>
              <div className="col-span-3">
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  className={
                    errors.address ? "border-red-500 focus:border-red-500" : ""
                  }
                  placeholder="Enter positive integer (e.g., 1, 2, 100)"
                />
                {errors.address && (
                  <p className="text-sm text-red-500 mt-1">{errors.address}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <div className="col-span-3">
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  className={
                    errors.description
                      ? "border-red-500 focus:border-red-500"
                      : ""
                  }
                  placeholder="Enter description"
                />
                {errors.description && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                loading || Object.keys(errors).some((key) => errors[key])
              }
            >
              {loading ? "Saving..." : mode === "edit" ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
